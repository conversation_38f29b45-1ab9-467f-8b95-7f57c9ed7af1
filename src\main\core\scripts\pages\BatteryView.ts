  import { invoke } from "@tauri-apps/api/core";
  import { listen } from '@tauri-apps/api/event';
  import { BatteryData } from "../interfaces/batteryData";
  import { getBatteryType } from "../../dataContainer/BatteryDataContainer";

  console.log("BatteryView.ts 已載入");



  // // API 服務
  // class BatteryApiService {
  //   private apiUrl = 'http://localhost:8080/api';

  //   // 獲取所有電池
  //   async getAllBatteries(): Promise<Battery[]> {     
  //     try {
  //       const response = await fetch(`${this.apiUrl}/batteries`, {
  //         method: 'GET',
  //         headers: {
  //           'Accept': 'application/json'
  //         }
  //       });
  //       if (!response.ok) {
  //         throw new Error(`HTTP error! status: ${response.status}`);
  //       }
  //       const result = await response.json();
  //       return result.data;
  //     } catch (error) {
  //       console.error("獲取電池列表失敗:", error);
  //       return [];
  //     }
  //   }

  //   // 獲取單個電池
  //   async getBattery(id: string): Promise<Battery | null> {
  //     try {
  //       const response = await fetch(`${this.apiUrl}/batteries/${id}`, {
  //         method: 'GET',
  //         headers: {
  //           'Accept': 'application/json'
  //         }
  //       });
  //       if (!response.ok) {
  //         throw new Error(`HTTP error! status: ${response.status}`);
  //       }
  //       const result = await response.json();
  //       return result.data;
  //     } catch (error) {
  //       console.error(`獲取電池 ${id} 失敗:`, error);
  //       return null;
  //     }
  //   }

  //   // 新增電池
  //   async addBattery(battery: Omit<Battery, "id">): Promise<Battery | null> {
  //     try {
  //       console.log("battery", battery);
  //       const response = await fetch(`${this.apiUrl}/batteries`, {
  //         method: 'POST',
  //         headers: {
  //           'Content-Type': 'application/json',
  //           'Accept': 'application/json'
  //         },
  //         body: JSON.stringify(battery)
  //       });
  //       console.log("response", response);
  //       if (!response.ok) {
  //         throw new Error(`HTTP error! status: ${response.status}`);
  //       }

  //       const result = await response.json();
  //       return result.data;
  //     } catch (error) {
  //       console.error("新增電池失敗:", error);
  //       return null;
  //     }
  //   }

  //   // 修改電池
  //   async updateBattery(id: string, battery: Partial<Battery>): Promise<boolean> {
  //     try {
  //       const response = await fetch(`${this.apiUrl}/batteries/${id}`, {
  //         method: 'PUT',
  //         headers: {
  //           'Content-Type': 'application/json'
  //         },
  //         body: JSON.stringify(battery)
  //       });
  //       if (!response.ok) {
  //         throw new Error(`HTTP error! status: ${response.status}`);
  //       }
  //       return true;
  //     } catch (error) {
  //       console.error(`修改電池 ${id} 失敗:`, error);
  //       return false;
  //     }
  //   }

  //   // 刪除電池
  //   async deleteBattery(id: string): Promise<boolean> {
  //     try {
  //       const response = await fetch(`${this.apiUrl}/batteries/${id}`, {
  //         method: 'DELETE'
  //       });
  //       if (!response.ok) {
  //         throw new Error(`HTTP error! status: ${response.status}`);
  //       }
  //       return true;
  //     } catch (error) {
  //       console.error(`刪除電池 ${id} 失敗:`, error);
  //       return false;
  //     }
  //   }
  // }

  // // 電池管理
  // export class BatteryViewController {
  //   private apiService: BatteryApiService;
  //   private batteriesContainer: HTMLElement | null;
    
  //   constructor() {
  //     this.apiService = new BatteryApiService();
  //     this.batteriesContainer = document.getElementById("batteries-container");
  //   }

  //   // 初始化
  //   async initialize() {
  //     await this.loadBatteries();
  //     this.setupEventListeners();
  //   }

  //   // 讀取電池列表
  //   async loadBatteries() {
  //     if (!this.batteriesContainer) 
  //       {
  //         console.error("Element batteries-container not found");
  //         return
  //       };
  //     const batteries = await this.apiService.getAllBatteries();
  //     console.log("batteries", batteries);
  //     // 更新統計卡片
  //     this.updateStatistics(batteries);
      
  //     if (batteries.length === 0) {
  //       this.batteriesContainer.innerHTML = `
  //         <div class="text-center p-6">
  //           <p class="text-gray-400">尚無電池資料</p>
  //           <button id="add-battery-btn" class="mt-4 px-4 py-2 bg-yellow-500 text-black rounded font-bold">
  //             新增電池
  //           </button>
  //         </div>
  //       `;
  //       return;
  //     }
      
  //     this.renderBatteries(batteries);
  //   }

  //   // 組合電池列表 HTML
  //   renderBatteries(batteries: Battery[]) {
  //     if (!this.batteriesContainer) return;
      
  //     let html = `
  //       <div class="flex justify-between mb-4">
  //         <h3 class="text-lg font-bold">電池列表</h3>
  //         <button id="add-battery-btn" onclick="showBatteryForm()" class="px-3 py-1 bg-yellow-500 text-black rounded text-sm">
  //           新增電池
  //         </button>
  //       </div>
  //       <div class="overflow-x-auto">
  //         <table class="w-full">
  //           <thead>
  //             <tr class="text-left text-gray-400 text-sm">
  //               <th class="pb-3">ID</th>
  //               <th class="pb-3">名稱</th>
  //               <th class="pb-3">狀態</th>
  //               <th class="pb-3">容量</th>
  //               <th class="pb-3">最後充電</th>
  //               <th class="pb-3">操作</th>
  //             </tr>
  //           </thead>
  //           <tbody class="text-sm">
  //     `;
      
  //     batteries.forEach(battery => {
  //       html += `
  //         <tr class="border-t border-gray-700" data-id="${battery.id}">
  //           <td class="py-3">${battery.id}</td>
  //           <td class="py-3">${battery.name}</td>
  //           <td class="py-3">${this.getStatusBadge(battery.status)}</td>
  //           <td class="py-3">${battery.capacity}%</td>
  //           <td class="py-3">${battery.last_charged}</td>
  //           <td class="py-3">
  //             <button class="edit-btn px-2 py-1 bg-blue-600 text-white rounded-sm mr-2">編輯</button>
  //             <button class="delete-btn px-2 py-1 bg-red-600 text-white rounded-sm">刪除</button>
  //           </td>
  //         </tr>
  //       `;
  //     });
      
  //     html += `
  //           </tbody>
  //         </table>
  //       </div>
  //     `;
      
  //     this.batteriesContainer.innerHTML = html;
  //   }

  //   // 獲取狀態標籤
  //   getStatusBadge(status: string): string {
  //     const statusMap: Record<string, { bg: string, text: string }> = {
  //       'normal': { bg: 'bg-green-900', text: 'text-green-300' },
  //       'charging': { bg: 'bg-blue-900', text: 'text-blue-300' },
  //       'low': { bg: 'bg-yellow-900', text: 'text-yellow-300' },
  //       'critical': { bg: 'bg-red-900', text: 'text-red-300' }
  //     };

  //     const statusNameMap: Record<string, { zh: string }> = {
  //       'normal': { zh: '正常' },
  //       'charging': { zh: '充電中' },
  //       'low': { zh: '低電量' },
  //       'critical': { zh: '電量危急' }
  //     };
      
  //     const style = statusMap[status.toLowerCase()] || { bg: 'bg-gray-900', text: 'text-gray-300', zh: 'null' };
  //     const name = statusNameMap[status.toLowerCase()] || { zh: status };
  //     return `<span class="px-2 py-1 ${style.bg} ${style.text} rounded-full text-xs">${name.zh}</span>`;
  //   }

  //   // 設置事件監聽器
  //   setupEventListeners() {
  //     if (!this.batteriesContainer) return;
      
  //     // 新增電池按鈕
  //     const addBtn = document.getElementById("add-battery-btn");
  //     if (addBtn) {
  //       addBtn.addEventListener("click", () => this.showBatteryForm());
  //     }
      
  //     // 編輯和刪除按鈕
  //     this.batteriesContainer.addEventListener("click", (e) => {
  //       const target = e.target as HTMLElement;
  //       const row = target.closest("tr");
  //       if (!row) return;
        
  //       const batteryId = row.getAttribute("data-id");
  //       if (!batteryId) return;
        
  //       if (target.classList.contains("edit-btn")) {
  //         this.showBatteryForm(batteryId);
  //       } else if (target.classList.contains("delete-btn")) {
  //         this.confirmDeleteBattery(batteryId);
  //       }
  //     });
  //   }

  //   // 新增、修改電池表單
  //   async showBatteryForm(batteryId?: string) {
  //     alert(':showBatteryForm');
  //     let battery: Battery | null = null;
      
  //     if (batteryId) {
  //       battery = await this.apiService.getBattery(batteryId);
  //       if (!battery) return;
  //     }
      
  //     const modal = document.createElement("div");
  //     modal.className = "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";
  //     modal.innerHTML = `
  //       <div class="bg-gray-800 p-6 rounded-lg w-full max-w-md">
  //         <h3 class="text-lg font-bold mb-4">${batteryId ? '編輯電池' : '新增電池'}</h3>
  //         <form id="battery-form">
  //           ${batteryId ? `<input type="hidden" id="battery-id" value="${batteryId}">` : ''}
  //           <div class="mb-4">
  //             <label class="block text-sm text-gray-400 mb-1">名稱</label>
  //             <input type="text" id="battery-name" class="w-full p-2 bg-gray-700 rounded" value="${battery?.name || ''}">
  //           </div>
  //           <div class="mb-4">
  //             <label class="block text-sm text-gray-400 mb-1">狀態</label>
  //             <select id="battery-status" class="w-full p-2 bg-gray-700 rounded">
  //               <option value="normal" ${battery?.status === 'normal' ? 'selected' : ''}>正常</option>
  //               <option value="charging" ${battery?.status === 'charging' ? 'selected' : ''}>充電中</option>
  //               <option value="low" ${battery?.status === 'low' ? 'selected' : ''}>低電量</option>
  //               <option value="critical" ${battery?.status === 'critical' ? 'selected' : ''}>電量危急</option>
  //             </select>
  //           </div>
  //           <div class="mb-4">
  //             <label class="block text-sm text-gray-400 mb-1">容量 (%)</label>
  //             <input type="number" id="battery-capacity" min="0" max="100" class="w-full p-2 bg-gray-700 rounded" value="${battery?.capacity || 100}">
  //           </div>
  //           <div class="mb-4">
  //             <label class="block text-sm text-gray-400 mb-1">最後充電時間</label>
  //             <input type="datetime-local" id="battery-last-charged" class="w-full p-2 bg-gray-700 rounded" value="${battery?.last_charged ? battery.last_charged.replace(' ', 'T') : new Date().toISOString().slice(0, 16)}">
  //           </div>
  //           <div class="flex justify-end space-x-2">
  //             <button type="button" id="cancel-form" class="px-4 py-2 bg-gray-600 rounded">取消</button>
  //             <button type="submit" class="px-4 py-2 bg-yellow-500 text-black rounded font-bold">儲存</button>
  //           </div>
  //         </form>
  //       </div>
  //     `;
      
  //     document.body.appendChild(modal);
      
  //     // 表單取消按鈕
  //     const cancelBtn = document.getElementById("cancel-form");
  //     if (cancelBtn) {
  //       cancelBtn.addEventListener("click", () => {
  //         document.body.removeChild(modal);
  //       });
  //     }
      
  //     // 表單提交
  //     const form = document.getElementById("battery-form") as HTMLFormElement;
  //     if (form) {
  //       form.addEventListener("submit", async (e) => {
  //         e.preventDefault();
          
  //         const nameInput = document.getElementById("battery-name") as HTMLInputElement;
  //         const statusSelect = document.getElementById("battery-status") as HTMLSelectElement;
  //         const capacityInput = document.getElementById("battery-capacity") as HTMLInputElement;
  //         const lastChargedInput = document.getElementById("battery-last-charged") as HTMLInputElement;
          
  //         const batteryData = {
  //           name: nameInput.value,
  //           status: statusSelect.value,
  //           capacity: parseInt(capacityInput.value),
  //           last_charged: lastChargedInput.value.replace('T', ' ')
  //         };
          
  //         let success = false;
          
  //         if (batteryId) {
  //           // 更新電池
  //           success = await this.apiService.updateBattery(batteryId, batteryData);
  //         } else {
  //           // 新增電池
  //           const newBattery = await this.apiService.addBattery(batteryData);
  //           success = !!newBattery;
  //         }
          
  //         if (success) {
  //           document.body.removeChild(modal);
  //           await this.loadBatteries();
  //         } else {
  //           alert("操作失敗，請稍後再試");
  //         }
  //       });
  //     }
  //   }

  //   // 確認刪除電池
  //   async confirmDeleteBattery(batteryId: string) {
  //     if (confirm("確定要刪除此電池嗎？此操作無法復原。")) {
  //       const success = await this.apiService.deleteBattery(batteryId);
  //       if (success) {
  //         await this.loadBatteries();
  //       } else {
  //         alert("刪除失敗，請稍後再試");
  //       }
  //     }
  //   }

  //   // 更新統計卡片
  //   updateStatistics(batteries: Battery[]) {
  //     const totalElement = document.getElementById("total-batteries");
  //     const normalElement = document.getElementById("normal-batteries");
  //     const chargingElement = document.getElementById("charging-batteries");
  //     const lowElement = document.getElementById("low-batteries");
      
  //     if (totalElement) totalElement.textContent = batteries.length.toString();
      
  //     // 計算各種狀態的電池數量
  //     const normalCount = batteries.filter(b => b.status.toLowerCase() === 'normal').length;
  //     const chargingCount = batteries.filter(b => b.status.toLowerCase() === 'charging').length;
  //     const lowCount = batteries.filter(b => b.status.toLowerCase() === 'low' || b.status.toLowerCase() === 'critical').length;
      
  //     if (normalElement) normalElement.textContent = normalCount.toString();
  //     if (chargingElement) chargingElement.textContent = chargingCount.toString();
  //     if (lowElement) lowElement.textContent = lowCount.toString();
  //   }
  // }



  // const tbxProduct = document.getElementById("product") as HTMLElement;
  // const tbxFirmwareVersion = document.getElementById("firmware-version") as HTMLInputElement;
  // const tbxSerialNumber = document.getElementById("serial-number") as HTMLInputElement;


  // let tbxProduct: HTMLElement | null;
  // let tbxFirmwareVersion: HTMLElement | null;
  // let tbxSerialNumber: HTMLElement | null;
  let tbxProduct;
  let tbxFirmwareVersion;
  let tbxSerialNumber;

  let timeoutID = -1;
  // 全域變數儲存電池資料
  var currentBatteryData: BatteryData[] = [];

  async function delay(ms: number) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // 更新統計資訊
  function updateStatistics(batteryDataList: BatteryData[]) {
    const totalElement = document.getElementById("total-batteries");
    const normalElement = document.getElementById("normal-batteries");
    const chargingElement = document.getElementById("charging-batteries");
    const lowElement = document.getElementById("low-batteries");

    // 過濾有效電池（有序號的電池）
    const validBatteries = batteryDataList.filter(battery => battery.sn !== '');
    
    if (totalElement) totalElement.textContent = validBatteries.length.toString();

    // 計算各種狀態的電池數量
    let normalCount = 0;
    let chargingCount = 0;
    let lowCount = 0;

    validBatteries.forEach(battery => {
      const batteryLevel = battery.relativeStateOfCharged || 0;
      const current = battery.current || 0;

      if (current > 0) {
        chargingCount++; // 正在充電
      } else if (batteryLevel < 20) {
        lowCount++; // 低電量
      } else {
        normalCount++; // 正常
      }
    });

    if (normalElement) normalElement.textContent = normalCount.toString();
    if (chargingElement) chargingElement.textContent = chargingCount.toString();
    if (lowElement) lowElement.textContent = lowCount.toString();
  }

  

  // 顯示電池詳細資訊
  (window as any).showBatteryDetails = function(slotId: number) {
    //alert("showBatteryDetails");
    //alert("slotId: " + slotId);
    console.log("batteries: ", currentBatteryData);
    const batteryData = currentBatteryData.find(battery => battery.id === slotId);
    console.log("battery: ", batteryData);
    if (!batteryData || batteryData.sn === '') return;

    const modal = document.getElementById('battery-details-modal');
    const content = document.getElementById('battery-details-content');
    
    if (!modal || !content) return;

    content.innerHTML = `
      <div class="details-grid">
        <div class="detail-section">
          <h4>📦基本資訊</h4>
          <div class="detail-item">
            <span class="detail-label">ID:</span>
            <span class="detail-value">${batteryData.id}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">SN 序號:</span>
            <span class="detail-value">${batteryData.sn}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Battery Type 電池類型:</span>
            <span class="detail-value">${getBatteryType(batteryData.sn)}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">PACK 電池包數量:</span>
            <span class="detail-value">${batteryData.pack}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">MODE 模式碼:</span>
            <span class="detail-value">${batteryData.mode}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>🔋電池資訊與製造區塊</h4>
          <div class="detail-item">
            <span class="detail-label">Device Name 設備名稱:</span>
            <span class="detail-value">${batteryData.deviceName || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">GaugeStatus 電量計狀態碼:</span>
            <span class="detail-value">${batteryData.gaugeStatus}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Error 錯誤代碼:</span>
            <span class="detail-value">${batteryData.error}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">ManufactureDate 製造日期:</span>
            <span class="detail-value">${batteryData.manufactureDate || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">ManufactureBlock_1 製造資訊區塊1:</span>
            <span class="detail-value">${batteryData.manufactureBlock_1 || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">ManufactureBlock_2 製造資訊區塊2:</span>
            <span class="detail-value">${batteryData.manufactureBlock_2 || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">ManufactureBlock_3 製造資訊區塊3:</span>
            <span class="detail-value">${batteryData.manufactureBlock_3 || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">ManufactureBlock_4 製造資訊區塊4:</span>
            <span class="detail-value">${batteryData.manufactureBlock_4 || 'N/A'}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>📊電量資訊</h4>
          <div class="detail-item">
            <span class="detail-label">RelativeStateOfCharged 相對電量:</span>
            <span class="detail-value">${batteryData.relativeStateOfCharged} %</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">AbsoluteStateOfCharged 絕對電量:</span>
            <span class="detail-value">${batteryData.absoluteStateOfCharged} %</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">RemainingCapacity 剩餘容量:</span>
            <span class="detail-value">${batteryData.remainingCapacity} mAh</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">FullyChargedCapacity 滿充容量:</span>
            <span class="detail-value">${batteryData.fullyChargedCapacity} mAh</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">DesignVoltage 設計電壓:</span>
            <span class="detail-value">${batteryData.designVoltage} mV</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">DesignCapacity 設計容量:</span>
            <span class="detail-value">${batteryData.designCapacity} mAh</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>⚡ 電壓電流</h4>
          <div class="detail-item">
            <span class="detail-label">Voltage 電壓:</span>
            <span class="detail-value">${batteryData.voltage} mV</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Current 電流:</span>
            <span class="detail-value">${batteryData.current} mA</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">AverageCurrent 平均電流:</span>
            <span class="detail-value">${batteryData.averageCurrent} mA</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">ChargingVoltage 充電電壓:</span>
            <span class="detail-value">${batteryData.chargingVoltage} mV</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">ChargingCurrent 充電電流:</span>
            <span class="detail-value">${batteryData.chargingCurrent} mA</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">UntilFullyCharged 距離充滿所需時間:</span>
            <span class="detail-value">${batteryData.untilFullyCharged} min</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">UntilFullyDischarged 距離放完電時間:</span>
            <span class="detail-value">${batteryData.untilFullyDischarged} min</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>🌡️ 溫度與循環</h4>
          <div class="detail-item">
            <span class="detail-label">Temperature 溫度:</span>
            <span class="detail-value">${batteryData.temperature.toFixed(2)} °C</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">Cycle 循環次數:</span>
            <span class="detail-value">${batteryData.cycle}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>🔄 延伸資料 (XXL)</h4>
          <div class="detail-item">
            <span class="detail-label">CycleIndex 循環指數:</span>
            <span class="detail-value">${batteryData.cycleIndex}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">CycleThreshold 循環閾值:</span>
            <span class="detail-value">${batteryData.cycleThreshold}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">FullyChargedDate 最近一次充滿日期:</span>
            <span class="detail-value">${batteryData.fullyChargedDate || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">FullyChargedCapacityThreshold 完充容量門檻值:</span>
            <span class="detail-value">${batteryData.fullyChargedCapacityThreshold}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">FullyChargedCapacityBackup 完充容量備份值:</span>
            <span class="detail-value">${batteryData.fullyChargedCapacityBackup}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">XXLLifetimeMaxPackVoltage 生命週期中最大整包電壓:</span>
            <span class="detail-value">${batteryData.xxlLifetimeMaxPackVoltage} mV</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">XXLLifetimeMinPackVoltage 最小整包電壓:</span>
            <span class="detail-value">${batteryData.xxlLifetimeMinPackVoltage}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">XXLLifetimeMaxCurrent 最大電流:</span>
            <span class="detail-value">${batteryData.xxlLifetimeMaxCurrent} mA</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">XXLLifetimeMinCurrent 最小電流:</span>
            <span class="detail-value">${batteryData.xxlLifetimeMinCurrent} mA</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">OrangeLED 橘燈狀態:</span>
            <span class="detail-value">${batteryData.orangeLED}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">FullyChargedVoltage 完全充電時電壓:</span>
            <span class="detail-value">${batteryData.fullyChargedVoltage}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">FirstUseDate 首次使用日期:</span>
            <span class="detail-value">${batteryData.firstUseDate || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">RecordDate 紀錄日期:</span>
            <span class="detail-value">${batteryData.recordDate || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">RecordTime 紀錄時間:</span>
            <span class="detail-value">${batteryData.recordTime || 'N/A'}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">PackMode 電池包模式:</span>
            <span class="detail-value">${batteryData.packMode}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>🔋 電芯與電壓資訊</h4>
          <div class="detail-item">
            <span class="detail-label">CellVoltage_1 電芯1:</span>
            <span class="detail-value">${batteryData.cellVoltage_1} mV</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">CellVoltage_2 電芯2:</span>
            <span class="detail-value">${batteryData.cellVoltage_2} mV</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">CellVoltage_3 電芯3:</span>
            <span class="detail-value">${batteryData.cellVoltage_3} mV</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">CellVoltage_4 電芯4:</span>
            <span class="detail-value">${batteryData.cellVoltage_4} mV</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">PackVoltage 電池包總電壓:</span>
            <span class="detail-value">${batteryData.packVoltage} mV</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">FETControl 開關控制狀態:</span>
            <span class="detail-value">${batteryData.fetControl}</span>
          </div>
        </div>

        <div class="detail-section">
          <h4>🛡️ 安全與錯誤狀態</h4>
          <div class="detail-item">
            <span class="detail-label">SafetyAlert_1 安全警報:</span>
            <span class="detail-value">${batteryData.safetyAlert_1}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">SafetyAlert_2 安全警報:</span>
            <span class="detail-value">${batteryData.safetyAlert_2}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">SafetyStatus_1 安全狀態:</span>
            <span class="detail-value">${batteryData.safetyStatus_1}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">SafetyStatus_2 安全狀態:</span>
            <span class="detail-value">${batteryData.safetyStatus_2}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">PFAlert_1 永久失效警報:</span>
            <span class="detail-value">${batteryData.pfAlert_1}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">PFAlert_2 永久失效警報:</span>
            <span class="detail-value">${batteryData.pfAlert_2}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">PFStatus_1 永久失效狀態:</span>
            <span class="detail-value">${batteryData.pfStatus_1}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">PFStatus_2 永久失效狀態:</span>
            <span class="detail-value">${batteryData.pfStatus_2}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">OperationStatus 運行狀態 操作狀態碼:</span>
            <span class="detail-value">${batteryData.operationStatus}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">ChargingStatus 充電狀態碼:</span>
            <span class="detail-value">${batteryData.chargingStatus}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">TemperatureRange 溫度範圍:</span>
            <span class="detail-value">${batteryData.temperatureRange}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">MaxError 最大誤差:</span>
            <span class="detail-value">${batteryData.maxError} %</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeMaxTemperature 使用過程中最高溫度:</span>
            <span class="detail-value">${batteryData.lifetimeMaxTemperature} °C</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeMinTemperature 最低溫度:</span>
            <span class="detail-value">${batteryData.lifetimeMinTemperature} °C</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeAvgTemperature 平均溫度:</span>
            <span class="detail-value">${batteryData.lifetimeAvgTemperature}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeMaxCellVoltage 最大單顆電芯電壓:</span>
            <span class="detail-value">${batteryData.lifetimeMaxCellVoltage}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeMinCellVoltage 最小單顆電芯電壓:</span>
            <span class="detail-value">${batteryData.lifetimeMinCellVoltage}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeMaxPackVoltage 最大整包電壓:</span>
            <span class="detail-value">${batteryData.lifetimeMaxPackVoltage}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeMinPackVoltage 最小整包電壓:</span>
            <span class="detail-value">${batteryData.lifetimeMinPackVoltage}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeMaxChargingCurrent 最大充電電流:</span>
            <span class="detail-value">${batteryData.lifetimeMaxChargingCurrent}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeMaxDischargingCurrent 最大放電電流:</span>
            <span class="detail-value">${batteryData.lifetimeMaxDischargingCurrent}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeMaxAvgDischargingCurrent 最大平均放電電流:</span>
            <span class="detail-value">${batteryData.lifetimeMaxAvgDischargingCurrent}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeMaxChargingPower 最大充電功率:</span>
            <span class="detail-value">${batteryData.lifetimeMaxChargingPower}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeMaxDischargingPower 最大放電功率:</span>
            <span class="detail-value">${batteryData.lifetimeMaxDischargingPower}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeMaxAvgDischargingPower 最大平均放電功率:</span>
            <span class="detail-value">${batteryData.lifetimeMaxAvgDischargingPower}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">LifetimeTemperatureSamples 溫度樣本總數:</span>
            <span class="detail-value">${batteryData.lifetimeTemperatureSamples}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">OTEventCount 過溫事件次數:</span>
            <span class="detail-value">${batteryData.otEventCount}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">OTEventDuration 過溫事件總時長:</span>
            <span class="detail-value">${batteryData.otEventDuration}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">OVEventCount 過壓事件次數:</span>
            <span class="detail-value">${batteryData.ovEventCount}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">OVEventDuration 過壓事件總時長:</span>
            <span class="detail-value">${batteryData.ovEventDuration}</span>
          </div>
          
        </div>
      </div>
    `;

    modal.classList.remove('hidden');
  };

  // 創建電池卡片
  function createBatteryCard(slotId: number, batteryData?: BatteryData): HTMLElement {
    const card = document.createElement('div');
    card.className = `battery-card ${!batteryData || batteryData.sn === '' ? 'no-battery' : ''}`;
    console.log("batteryData Data:", batteryData);
    if (batteryData && batteryData.sn !== '') {
      // 有電池的情況
      const batteryLevel = (batteryData && batteryData.relativeStateOfCharged !== undefined) ? batteryData.relativeStateOfCharged : 0;
      const levelClass = batteryLevel > 60 ? 'high' : batteryLevel > 30 ? 'medium' : 'low';
      const isCharging = batteryData.current > 0;
      console.log("batteryData.relativeStateOfCharged :", batteryData?.relativeStateOfCharged );
      console.log("batteryLevel:", batteryLevel);
      console.log("isCharging:", isCharging, "current:", batteryData.current);
      card.innerHTML = `
        <div class="battery-icon">
          <div class="battery-level ${levelClass}" style="height: ${batteryLevel}%"></div>
          <div class="battery-percentage" style="color: ${batteryLevel > 60 ? '#10b981' : batteryLevel > 30 ? '#f59e0b' : '#ef4444'}; position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); font-size: 0.8em; font-weight: bold; text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);">${batteryLevel}%</div>
          ${isCharging ? '<div class="charging-icon">⚡</div>' : ''}
        </div>
        <div class="serial-number">${batteryData.sn}</div>
        <div class="battery-info-grid">
          <div class="info-item">
            <div class="info-label">Current</div>
            <div class="info-value">${batteryData.current} mA</div>
          </div>
          <div class="info-item">
            <div class="info-label">Voltage</div>
            <div class="info-value">${batteryData.voltage} mV</div>
          </div>
          <div class="info-item">
            <div class="info-label">Temperature</div>
            <div class="info-value">${batteryData.temperature.toFixed(2)} °C</div>
          </div>
          <div class="info-item">
            <div class="info-label">Cycle</div>
            <div class="info-value">${batteryData.cycle}</div>
          </div>
        </div>
        <button class="details-btn" onclick="showBatteryDetails(${slotId})">Details</button>
      `;
    } else {
      // 無電池的情況
      card.innerHTML = `
        <div class="battery-icon">
          <div class="battery-percentage" style="color: #6b7280">--</div>
        </div>
        <div class="serial-number">No Battery</div>
        <div class="battery-info-grid">
          <div class="info-item">
            <div class="info-label">Current</div>
            <div class="info-value">-- mA</div>
          </div>
          <div class="info-item">
            <div class="info-label">Voltage</div>
            <div class="info-value">-- mV</div>
          </div>
          <div class="info-item">
            <div class="info-label">Temperature</div>
            <div class="info-value">-- °C</div>
          </div>
          <div class="info-item">
            <div class="info-label">Cycle</div>
            <div class="info-value">--</div>
          </div>
        </div>
        <button class="details-btn" disabled style="opacity: 0.5; cursor: not-allowed;">No Data</button>
      `;
    }

    return card;
  }

  // 更新電池顯示
  function updateBatteryDisplay(batteryDataList: BatteryData[]) {
    // 電池資料給全域變數
    currentBatteryData = batteryDataList;

    console.log("batteryDataList Data:", batteryDataList);
    //console.log("currentBatteryData Data:", currentBatteryData);
    const batteryGrid = document.getElementById('battery-grid');
    if (!batteryGrid) return;

    // 清空現有內容
    batteryGrid.innerHTML = '';

    // 電池排列順序：5 3 1 / 4 2 0
    const batteryOrder = [5, 3, 1, 4, 2, 0];

    // 根據指定順序創建電池卡片
    batteryOrder.forEach(index => {
      const batteryData = batteryDataList.find(battery => battery.id === index);
      const batteryCard = createBatteryCard(index, batteryData);
      batteryGrid.appendChild(batteryCard);
    });

    // 更新統計資訊
    updateStatistics(batteryDataList);
  }

  async function showBatteries() {
    try {
      console.log("開始獲取電池資料...");
      /// 調用 Charger_GetBatteries 並儲存回傳資料
      const batteryDataList: BatteryData[] = await invoke<BatteryData[]>("Charger_GetBatteries");
      /// 顯示資料
      console.log("Battery Data:", batteryDataList);
      
      // 更新電池卡片顯示
      updateBatteryDisplay(batteryDataList);
      
      /// 逐項顯示每個電池的資料（保留舊的動畫效果，如果需要的話）
      // batteryDataList.forEach((data, index) => {
      //   console.log(`Battery ${index + 1}: ID=${data.id}, SN=${data.sn}`);
      //   if(data.sn !== "") {
      //     insertBattery(index + 1);
      //   }else{
      //     removeBattery(index + 1);
      //   }
      // });
      console.log("電池資料獲取完成");
    } catch (error) {
      console.error("獲取電池資料時發生錯誤:", error);
      // 停止定時器以防止持續錯誤
      if (timeoutID !== -1) {
        window.clearInterval(timeoutID);
        timeoutID = -1;
        console.log("已停止定時器以防止持續錯誤");
      }
    }
  }

  export async function Initial() {
    try {
      console.log("開始初始化...");
      
      // 清除現有的定時器
      if (timeoutID !== -1) {
        //window.clearInterval(timeoutID);
        timeoutID = -1;
        console.log("已清除現有定時器");
      }
      
      const vList = [0x0000FFFF, 0xFFFF0000, 0x03EB4736];
      for (const product of vList) {
        console.log(`嘗試初始化產品: 0x${product.toString(16)}`);
        
        if (await invoke("Initial", { product })) {
          console.log(`產品 0x${product.toString(16)} 初始化成功，等待 5 秒...`);
          await delay(300);
          
          tbxProduct = await invoke("Charger_GetName");
          tbxFirmwareVersion = await invoke("Charger_GetVersion");
          tbxSerialNumber = await invoke("Charger_GetSerialNumber");
          
          console.log("Product:", tbxProduct, "Firmware:", tbxFirmwareVersion, "Serial:", tbxSerialNumber);
          
          // 先執行一次電池資料獲取測試
          console.log("執行初始電池資料獲取測試...");
          // await showBatteries();
          
          // 如果測試成功，才開始定時器
          console.log("開始設定定時器，每 6 秒獲取一次電池資料");
          timeoutID = window.setInterval(() => showBatteries(), 6000);
          
          console.log("初始化完成");
          return;
        }
      }
      
      console.log("所有產品初始化失敗");
      tbxProduct = "NA";
      tbxFirmwareVersion = "NA";
      tbxSerialNumber = "NA";
    } catch (error) {
      console.error("初始化過程中發生錯誤:", error);
      // 確保清除定時器
      if (timeoutID !== -1) {
        window.clearInterval(timeoutID);
        timeoutID = -1;
      }
      tbxProduct = "ERROR";
      tbxFirmwareVersion = "ERROR";
      tbxSerialNumber = "ERROR";
    }
  }

  export async function get_batteries(){
    // 儀表板頁面的初始化邏輯
    try {
      if (await invoke("LOAD_SDK")) {
        listen("USB_ChangedEvent", async () => {
          Initial();
        });
        
        Initial();

        // 設置關閉詳細資訊彈窗的事件監聽器
        const closeBtn = document.getElementById('close-details-btn');
        const modal = document.getElementById('battery-details-modal');
        
        if (closeBtn && modal) {
          closeBtn.addEventListener('click', () => {
            modal.classList.add('hidden');
          });
        }

        // 點擊彈窗外部關閉
        if (modal) {
          modal.addEventListener('click', (e) => {
            if (e.target === modal) {
              modal.classList.add('hidden');
            }
          });
        }
      }
    }
    catch (error) {
      console.error("Failed to load library:", error);
    }
  }

  // function insertBattery(num: number) {
  //     const battery = document.getElementById(`battery${num}`);
  //     const slot = document.getElementById(`slot${num}`);

  //     if (!battery || !slot) return;
      
  //     battery.style.visibility = 'visible';
  //     battery.classList.remove('out');
  //     battery.classList.add('in');
  //     slot.style.display = 'none';
  // }

  // function removeBattery(num: number) {
  //     const battery = document.getElementById(`battery${num}`);
  //     const slot = document.getElementById(`slot${num}`);

  //     if (!battery || !slot) return;

  //     battery.classList.remove('in');
  //     battery.classList.add('out');
  //     slot.style.display = 'block';
  //     // 等待動畫完成後隱藏（1秒 = transition 時間）
  //     setTimeout(() => {
  //         battery.style.visibility = 'hidden';
  //     }, 600);
  // }

//batteriesConn-container




// (async function() {
  
// })();




  // // 初始化控制器
  // window.addEventListener('DOMContentLoaded', () => {
  //   // 儀表板頁面的初始化邏輯
  //   console.log("DOMContentLoaded");
    
  // });

  // window.addEventListener("DOMContentLoaded", async () => {
  //   console.log("DOMContentLoaded async");
  // });

  // (function() {
  //   // 儀表板頁面的初始化邏輯
  //   console.log("初始化設定頁面");
  //   const controller = new BatteryViewController();
  //   controller.initialize();
  // })();

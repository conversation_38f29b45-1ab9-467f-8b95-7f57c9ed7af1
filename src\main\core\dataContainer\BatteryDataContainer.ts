import { invoke } from "@tauri-apps/api/core";
import { listen } from '@tauri-apps/api/event';
import { BatteryData } from "../scripts/interfaces/batteryData";

// 定義 BatteryData 的 TypeScript 型別，與 Rust 的 BatteryData 對應
export function getBatteryType(serialNumber: string): string {
    if (serialNumber.toUpperCase().includes("9789S19") || serialNumber.toUpperCase().includes("1750096")) {
      return "Standard";
    } else if (serialNumber.toUpperCase().includes("9789BAXL")) {
      return "XL";
    } else if (serialNumber.toUpperCase().includes("9789BXXL")) {
      return "XXL";
    } else if (serialNumber.toUpperCase().includes("OMD")) {
      return "Mini";
    } else {
      return "Unknown";
    }
}
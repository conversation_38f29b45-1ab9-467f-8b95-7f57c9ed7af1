/* 導入 Tailwind 的基礎樣式 */
@tailwind base;
@tailwind components;
@tailwind utilities;


:root {
    --bg-color: #1E2126;
    --text-color: #f6f6f6;
}
  
  /* .dark {
    --bg-color: #1E2126;
    --text-color: #f6f6f6;
  } */
/* body {
    background-color: #1E2126;
    color: white;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
} */
body {
    background-color: var(--bg-color);
    color: var(--text-color);
    /* transition: background-color 0.3s, color 0.3s; */
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}
  
  body.light-mode {
    --bg-color: #f8f9fa;
    --text-color: #333333;
    --card-bg: #ffffff;
    --card-border: rgba(0, 0, 0, 0.1);
    --sidebar-bg: #f0f2f5;
    --hover-bg: rgba(0, 0, 0, 0.05);
    --accent-color: #0056b3;
    --secondary-text: #6c757d;
    --input-bg: #ffffff;
    --input-border: #ced4da;
  }
  
  body.light-mode .card,
  body.light-mode .stat-card,
  body.light-mode .info-card {
    background-color: var(--card-bg);
    border: 1px solid var(--card-border);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  }
  body.light-mode .info-card {
    background-color: var(--bg-color);
    color: var(--text-color);
  }

  body.dark-mode {
    --bg-color: #1E2126;
    --text-color: #f6f6f6;
  }

  .bg-darker-gray{
    background-color: #282D33;
  }
  .bg-yellow-800{
    background-color: #F9A825;
  }
.card {
    background-color: #282D33;
    border-radius: 8px;
}
.card {
    background-color: var(--bg-color);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    transition: background-color 0.3s;
  }
  
  
.sidebar {
    background-color: #23272C;
    background-color: var(--bg-color);
    color: var(--text-color);
    width: 180px;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
}
  /* .sidebar.light-mode{
    background-color: var(--bg-color);
    color: var(--text-color);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    transition: background-color 0.3s;
  }
  .sidebar.dark-mode{
    background-color: var(--bg-color);
    color: var(--text-color);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    transition: background-color 0.3s;
  } */
.main-content {
    margin-left: 180px;
    padding: 20px;
}
.menu-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: white;
    color: var(--text-color);
    text-decoration: none;
}
.menu-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}
.menu-item.active {
    background-color: #3a4149;
    border-left: 4px solid #FFD700;
    color: #FFD700;
    font-weight: 500;
}
.menu-icon {
    margin-right: 12px;
    color: white;
    color: var(--text-color);
    width: 20px;
    height: 20px;
}
.stat-card {
    background-color: #282D33;
    background-color: var(--bg-color);
    /* color: var(--text-color); */
    border-radius: 8px;
    padding: 16px;
}
.yellow-accent {
    color: #FFD700;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .sidebar {
    width: 80px;
    }
    .menu-item span {
    display: none;
    }
    .menu-icon {
    /*margin-right: 0;*/
    }
    .main-content {
    margin-left: 80px;
    }
}

@media (max-width: 640px) {
    .sidebar {
    width: 100%;
    height: auto;
    position: relative;
    }
    .menu-item {
    display: inline-block;
    padding: 10px;
    }
    .menu-icon {
    margin-right: 0;
    }
    .main-content {
    margin-left: 0;
    }
}

body.light-mode .sidebar {
  background-color: var(--sidebar-bg);
  border-right: 1px solid var(--card-border);
}

body.light-mode .menu-item {
  color: var(--text-color);
}

body.light-mode .menu-item:hover {
  background-color: var(--hover-bg);
}

body.light-mode .menu-item.active {
  background-color: rgba(0, 86, 179, 0.1);
  border-left: 4px solid var(--accent-color);
  color: var(--accent-color);
}

body.light-mode .menu-icon {
  color: var(--text-color);
}

body.light-mode input,
body.light-mode select,
body.light-mode textarea {
  background-color: var(--input-bg);
  border: 1px solid var(--input-border);
  color: var(--text-color);
}

body.light-mode .yellow-accent {
  color: #d4a700;
}

body.light-mode button.primary {
  background-color: var(--accent-color);
  color: white;
}

/* 表格樣式 */
body.light-mode table {
  border-collapse: collapse;
  width: 100%;
}

body.light-mode th {
  background-color: #f5f5f5;
  border-bottom: 2px solid #dee2e6;
  color: #495057;
}

body.light-mode td {
  border-bottom: 1px solid #dee2e6;
}

body.light-mode tr:hover {
  background-color: rgba(0, 0, 0, 0.02);
}

/* 按鈕樣式 */
body.light-mode .btn {
  border-radius: 4px;
  padding: 0.375rem 0.75rem;
  transition: all 0.2s;
}

body.light-mode .btn-primary {
  background-color: #0056b3;
  color: white;
}

body.light-mode .btn-primary:hover {
  background-color: #004494;
}

body.light-mode .btn-secondary {
  background-color: #6c757d;
  color: white;
}

body.light-mode .btn-secondary:hover {
  background-color: #5a6268;
}

/* 通知和警告樣式 */
body.light-mode .alert {
  border-radius: 4px;
  padding: 0.75rem 1.25rem;
  margin-bottom: 1rem;
  border: 1px solid transparent;
}

body.light-mode .alert-info {
  background-color: #d1ecf1;
  border-color: #bee5eb;
  color: #0c5460;
}

body.light-mode .alert-warning {
  background-color: #fff3cd;
  border-color: #ffeeba;
  color: #856404;
}

body.light-mode .alert-danger {
  background-color: #f8d7da;
  border-color: #f5c6cb;
  color: #721c24;
}


<style>
  .serial-number {
    text-align: center;
    font-size: 14px;
    font-weight: 600;
    color: #05338f;
    margin-bottom: 5px;
    padding: 1px 6px;
    background: rgb(255 255 255 / 90%);
    border-radius: 13px;
    border: 1px solid #4b5563;
    /* position: absolute; */
    /* z-index: 99; */
    backdrop-filter: blur(4px);
  }
  .serial-number-top{
    bottom: 0px;
    left: 11px;
  }
  .serial-number-bottom{
    top: 10px;
    left: 11px;
  }

  /* 電池資訊卡片樣式 */
  .battery-info-card {
    /* position: absolute; */
    background: rgba(31, 41, 55, 0.95);
    background: rgb(134 148 148/80%);
    border: 1px solid #4b5563;
    border-radius: 8px;
    padding: 5px;
    font-size: 11px;
    color: #f3f4f6;
    z-index: 100;
    backdrop-filter: blur(6px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-width: 120px;
    margin: 10px 20px 0px 20px;
  }
  .battery-info-card-top {
    bottom: 5px;
    left: 11px;
  }
  .battery-info-card-bottom {
    top: 5px;
    left: 11px;
  }

  /* 健康度指示器 */
  .health-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 4px;
  }
  .health-good { background-color: #10b981; }
  .health-medium { background-color: #f59e0b; }
  .health-poor { background-color: #ef4444; }

  /* 懸停工具提示 */
  .battery-tooltip {
    position: absolute;
    background: rgba(17, 24, 39, 0.95);
    border: 1px solid #374151;
    border-radius: 8px;
    padding: 12px;
    font-size: 12px;
    color: #f3f4f6;
    z-index: 200;
    backdrop-filter: blur(8px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.4);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    pointer-events: none;
  }
  .battery-tooltip.show {
    opacity: 1;
    visibility: visible;
  }
  .battery-tooltip::before {
    content: '';
    position: absolute;
    top: -6px;
    left: 20px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid rgba(17, 24, 39, 0.95);
    transform: rotate(180deg);
  }

  .details-btn {
    /* width: 100%; */
    /* padding: 6px 12px; */
    padding: 2px 5px;
    /* background: #fff; */
    /* border: 1px solid #ccc; */
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    border: none;
    border-radius: 5px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 10px;
    display: none;
    z-index: 100;
    /* margin-top: 4px; */
  }

  .details-btn:hover {
    background: linear-gradient(135deg, #2563eb, #1e40af);
    transform: translateY(-1px);
  }

  /* 電池懸停效果 */
  .battery:hover {
    transform: scale(1.02);
    transition: transform 0.2s ease;
  }
  .details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }

  .detail-section {
    background: rgba(55, 65, 81, 0.5);
    padding: 16px;
    border-radius: 8px;
    border: 1px solid #4b5563;
  }

  .detail-section h4 {
    color: #f3f4f6;
    font-weight: 600;
    margin-bottom: 12px;
    border-bottom: 1px solid #4b5563;
    padding-bottom: 8px;
  }

  .detail-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 4px 0;
  }

  .detail-label {
    color: #9ca3af;
    font-size: 14px;
  }

  .detail-value {
    color: #f3f4f6;
    font-weight: 500;
    font-size: 14px;
  }
</style>
<style>
  /* body { font-family: Arial, sans-serif; margin: 20px; background-color: #f4f4f4; display: flex; flex-direction: column; align-items: center; height: 100vh; perspective: 1000px; } */
  .dock { 
    width: 815px; 
    height: 650px; 
    background: url('/src/main/assets/Upower622Box_800_600.png') no-repeat; 
    /* background-size: cover;  */
    background-size: inherit;
    /* border-radius: 10px;  */
    /* position: relative;  */
    overflow: hidden; 
    /* padding: 10px;  */
    /* box-shadow: 0 4px 8px rgba(0,0,0,0.2);  */
  }
  .battery-row { 
    display: flex; 
    justify-content: space-around; 
    position: relative; 
    margin: 35px 0; 
    height: 135px; 
  }
  .battery-row.top-row {
    margin-top: 146px;
  }
  .battery-row.bottom-row {
    margin-top: 37px;
  }
  .battery { 
    width: 215px; 
    height: 135px; 
    background: url('/src/main/assets/XL-Battery.png') no-repeat; 
    /* background: url('/src/main/assets/XL-Battery-2.png') no-repeat;  */
    background-size: cover; 
    /* border: 2px solid #999;  */
    position: absolute; 
    top: -280px; 
    transition: top 1s ease; 
    transform-style: preserve-3d; 
    /* box-shadow: 2px 2px 5px rgba(0,0,0,0.3);  */
    visibility: hidden; 
  }
  .battery-image { 
    background: url('/src/main/assets/XL-Battery.png') no-repeat; 
    /* background: url('/src/main/assets/XL-Battery-2.png') no-repeat;  */
    background-size: cover;
  }
  .battery-image-rotate-180 { 
    background: url('/src/main/assets/XL-Battery_rotate.png') no-repeat;
    /* background: url('/src/main/assets/XL-Battery-2.png') no-repeat;  */
    background-size: cover;
  }
  .battery-normal-image { 
    background: url('/src/main/assets/XL-Battery_normal.png') no-repeat;
    /* background: url('/src/main/assets/XL-Battery-2.png') no-repeat;  */
    background-size: cover;
  }
  .battery-normal-image-rotate-180 { 
    background: url('/src/main/assets/XL-Battery_normal_rotate.png') no-repeat;
    /* background: url('/src/main/assets/XL-Battery-2.png') no-repeat;  */
    background-size: cover;
  }
  .battery-charger-image { 
    background: url('/src/main/assets/XL-Battery_charger.png') no-repeat;
    /* background: url('/src/main/assets/XL-Battery-2.png') no-repeat;  */
    background-size: cover;
  }
  .battery-charger-image-rotate-180 { 
    background: url('/src/main/assets/XL-Battery_charger_rotate.png') no-repeat;
    /* background: url('/src/main/assets/XL-Battery-2.png') no-repeat;  */
    background-size: cover;
  }
  .battery-low-image { 
    background: url('/src/main/assets/XL-Battery_low.png') no-repeat;
    /* background: url('/src/main/assets/XL-Battery-2.png') no-repeat;  */
    background-size: cover;
  }
  .battery-low-image-rotate-180 { 
    background: url('/src/main/assets/XL-Battery_low_rotate.png') no-repeat;
    /* background: url('/src/main/assets/XL-Battery-2.png') no-repeat;  */
    background-size: cover;
  }
  .battery-broken-image { 
    background: url('/src/main/assets/XL-Battery_broken.png') no-repeat;
    /* background: url('/src/main/assets/XL-Battery-2.png') no-repeat;  */
    background-size: cover;
  }
  .battery-broken-image-rotate-180 { 
    background: url('/src/main/assets/XL-Battery_broken_rotate.png') no-repeat;
    /* background: url('/src/main/assets/XL-Battery-2.png') no-repeat;  */
    background-size: cover;
  }

  .battery-card {
      /* margin: 9px 12px; */
      width: 100%;
  }
  .image-rotate-0 { 
    transform: rotate(0deg); 
  }
  .image-rotate-180 { 
    transform: rotate(180deg); 
  }
  .battery.in { 
    top: 0; 
    visibility: visible; 
  }
  .battery.out { 
    top: -280px; 
  }
  .battery-info { 
    font-size: 12px; 
    text-align: center; 
    margin-top: 5px; 
    display: none; 
    background: rgba(255,255,255,0.8); 
    padding: 2px; border-radius: 3px; 
  }
  .battery.in .battery-info { display: block; }
  .battery-info-top { 
    /* position: relative;
    top: 5px; 
    left: 13px; */
  }
  .battery-info-bottom { 
    /* position: relative;
    top: 7px; 
    left: 13px; */
  }
  .battery.in .details-btn { display: block; }
  .slot { width: 215px; height: 135px; border: 2px dashed #ccc; position: absolute; top: 0; }
  .top-row .battery { transform: translateZ(20px); }
  .bottom-row .battery { transform: translateZ(-20px); }
  .controls { margin-top: 20px; text-align: center; }
  button { padding: 8px 16px; margin: 0 5px; cursor: pointer; }
</style>

<!-- 統計卡片 -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
  <div class="stat-card">
    <div class="flex justify-between items-center">
      <div>
        <div class="text-sm text-gray-400">總電池數</div>
        <div id="total-batteries" class="text-2xl font-bold">0</div>
      </div>
      <div class="text-accent-yellow">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path d="M13 7H7v6h6V7z"></path>
          <path fill-rule="evenodd" d="M7 2a1 1 0 00-.707 1.707L7.586 5H7a3 3 0 00-3 3v8a3 3 0 003 3h6a3 3 0 003-3V8a3 3 0 00-3-3h-.586l1.293-1.293A1 1 0 0016 2H7zm6 5a1 1 0 011 1v8a1 1 0 01-1 1H7a1 1 0 01-1-1V8a1 1 0 011-1h6z" clip-rule="evenodd"></path>
        </svg>
      </div>
    </div>
  </div>
  
  <div class="stat-card">
    <div class="flex justify-between items-center">
      <div>
        <div class="text-sm text-gray-400">正常電池</div>
        <div id="normal-batteries" class="text-2xl font-bold">0</div>
      </div>
      <div class="text-green-500">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
        </svg>
      </div>
    </div>
  </div>
  
  <div class="stat-card">
    <div class="flex justify-between items-center">
      <div>
        <div class="text-sm text-gray-400">資料不完整電池</div>
        <div id="incomplete-batteries" class="text-2xl font-bold">0</div>
      </div>
      <div class="text-yellow-500">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
      </div>
    </div>
  </div>

  <div class="stat-card">
    <div class="flex justify-between items-center">
      <div>
        <div class="text-sm text-gray-400">異常電池</div>
        <div id="error-batteries" class="text-2xl font-bold">0</div>
      </div>
      <div class="text-yellow-500">
        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
        </svg>
      </div>
    </div>
  </div>
</div>

<!-- 電池管理區塊 upower 62 -->
<div class="card p-6 mb-6">
  <div class="flex justify-between items-center mb-4">
    <h2 class="text-xl font-bold">電池管理</h2>
    <!-- <button id="add-battery-btn" class="px-4 py-2 bg-yellow-500 text-black rounded font-bold">新增電池</button> -->
  </div>

  <!-- 電池容器圖示 -->
  <div id="batteries-container2" class="mt-4">
    <!-- 電池列表將由 JavaScript 動態生成 -->
    <div class="text-center p-6 text-loading">
      <p class="text-gray-400">載入中...</p>
    </div>
    
    
    <div class="dock">
        <div class="battery-row top-row">
            <div class="slot" style="left: 60px;" id="slot5"></div>
            <div class="battery battery-image" id="battery5" style="left: 60px;">
                <!-- <img src="src/main/assets/XL-Battery_normal.png" alt="Battery Image" class="battery-image image-rotate-0"/> -->
                <!-- <div class="battery-info" id="info5">
                    <p>Serial: </p>
                    <p>Current: mA</p>
                    <p>Voltage: V</p>
                    <p>Temp: °C</p>
                    <p>Level: %</p>
                </div>
                <button class="details-btn battery-info-top" onclick="showDetails(5)">Details</button> -->
            </div>
            <div class="slot" style="left: 302px;" id="slot3"></div>
            <div class="battery battery-image" id="battery3" style="left: 302px;">
                <!-- <img src="src/main/assets/XL-Battery_normal.png" alt="Battery Image" class="battery-image image-rotate-0"/> -->
            </div>
            <div class="slot" style="left: 544px;" id="slot1"></div>
            <div class="battery battery-image" id="battery1" style="left: 544px;">
                <!-- <img src="src/main/assets/XL-Battery_normal.png" alt="Battery Image" class="battery-image image-rotate-0"/> -->
            </div>
        </div>
        <div class="battery-row bottom-row">
            <div class="slot" style="left: 60px;" id="slot4"></div>
            <div class="battery battery-image-rotate-180" id="battery4" style="left: 60px;">
                <!-- <img src="src/main/assets/XL-Battery_normal.png" alt="Battery Image" class="battery-image image-rotate-180"/> -->
            </div>
            <div class="slot" style="left: 302px;" id="slot2"></div>
            <div class="battery battery-image-rotate-180" id="battery2" style="left: 302px;">
                <!-- <img src="src/main/assets/XL-Battery_normal.png" alt="Battery Image" class="battery-image image-rotate-180"/> -->
            </div>
            <div class="slot" style="left: 544px;" id="slot0"></div>
            <div class="battery battery-image-rotate-180" id="battery0" style="left: 544px;">
                <!-- <img src="src/main/assets/XL-Battery_normal.png" alt="Battery Image" class="battery-image image-rotate-180"/> -->
            </div>
        </div>
    </div>
    <!-- 電池詳細資訊彈窗 -->
    <div id="battery-details-modal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
      <div class="bg-gray-800 p-6 rounded-lg w-full max-w-4xl max-h-[80vh] overflow-y-auto">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-bold text-gray-50">電池詳細資訊</h3>
          <button id="close-details-btn" class="text-gray-400 hover:text-white">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        <div id="battery-details-content">
          <!-- 詳細資訊內容將由 JavaScript 填入 -->
        </div>
      </div>
    </div>
    <!-- <div class="controls">
        <button id="btnInsert0" onclick="insertBattery(0)">Insert 1</button>
        <button id="btnRemove0" onclick="removeBattery(0)">Remove 1</button>
        <button id="btnInsert1" onclick="insertBattery(1)">Insert 2</button>
        <button id="btnRemove1" onclick="removeBattery(1)">Remove 2</button>
        <button id="btnInsert2" onclick="insertBattery(2)">Insert 3</button>
        <button id="btnRemove2" onclick="removeBattery(2)">Remove 3</button>
        <button id="btnInsert3" onclick="insertBattery(3)">Insert 4</button>
        <button id="btnRemove3" onclick="removeBattery(3)">Remove 4</button>
        <button id="btnInsert4" onclick="insertBattery(4)">Insert 5</button>
        <button id="btnRemove4" onclick="removeBattery(4)">Remove 5</button>
        <button id="btnInsert5" onclick="insertBattery(5)">Insert 6</button>
        <button id="btnRemove5" onclick="removeBattery(5)">Remove 6</button>
    </div> -->
    <div id="detailsPopup" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:white; padding:20px; border-radius:10px; box-shadow:0 0 10px rgba(0,0,0,0.1);">
        <h3>Details</h3>
        <ul id="detailsList"></ul>
        <button onclick="hideDetails()">Close</button>
    </div>

    <script>
        function insertBattery(num) {
            const battery = document.getElementById(`battery${num}`);
            const slot = document.getElementById(`slot${num}`);
            battery.style.visibility = 'visible';
            battery.classList.remove('out');
            battery.classList.add('in');
            slot.style.display = 'none';
        }

        const card = document.createElement('div');
        /*
        <div class="battery-info" id="info4">
            <p>Serial: BT001-004</p>
            <p>Current: 400mA</p>
            <p>Voltage: 3.7V</p>
            <p>Temp: 26°C</p>
            <p>Level: 80%</p>
        </div>
        */

        function removeBattery(num) {
            const battery = document.getElementById(`battery${num}`);
            const slot = document.getElementById(`slot${num}`);
            battery.classList.remove('in');
            battery.classList.add('out');
            slot.style.display = 'block';
            // 等待動畫完成後隱藏（1秒 = transition 時間）
            setTimeout(() => {
                battery.style.visibility = 'hidden';
            }, 600);
        }

        // function showDetails(num) {
        //     const popup = document.getElementById('detailsPopup');
        //     const detailsList = document.getElementById('detailsList');
        //     const info = document.getElementById(`info${num}`).innerHTML;
        //     detailsList.innerHTML = `
        //         <li>${info}</li>
        //         <li>Charge Cycles: </li>
        //         <li>Manufacture Date: </li>
        //         <li>Technology: </li>
        //         <li>Weight: g</li>
        //     `;
        //     popup.style.display = 'block';
        // }

        function hideDetails() {
            document.getElementById('detailsPopup').style.display = 'none';
        }
    </script>
    


  </div>
</div>




<!-- 載入 BatteryView.ts 腳本 -->
<!-- <script type="module" src="../../../core/scripts/pages/BatteryView.ts" defer></script> -->
<!-- <script type="module" crossorigin src="/src/main/core/scripts/pages/BatteryView.ts" defer></script>
<script>
    (function() {
      // 儀表板頁面的初始化邏輯
      console.log("初始化設定頁面");
      const controller = new BatteryViewController();
      controller.initialize();
    })();
</script> -->